#!/usr/bin/env python3
"""
统计日志文件中不同Problem Type和ra值的样本数量
"""

import re
from collections import defaultdict

def analyze_log_file(file_path):
    """
    分析日志文件，统计Problem Type和ra值的组合
    
    Args:
        file_path (str): 日志文件路径
    
    Returns:
        dict: 统计结果
    """
    # 初始化计数器
    stats = {
        'Easy': {'ra_1.0': 0, 'ra_0.0': 0},
        'Hard': {'ra_1.0': 0, 'ra_0.0': 0}
    }
    
    current_problem_type = None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                
                # 检查Problem Type行
                if line.startswith('Problem Type:'):
                    # 提取Problem Type (Easy 或 Hard)
                    match = re.search(r'Problem Type:\s*(Easy|Hard)', line)
                    if match:
                        current_problem_type = match.group(1)
                
                # 检查Completion行中的ra值
                elif line.startswith('Completion') and current_problem_type:
                    # 提取ra值
                    ra_match = re.search(r'ra=([01]\.0)', line)
                    if ra_match:
                        ra_value = ra_match.group(1)
                        if ra_value == '1.0':
                            stats[current_problem_type]['ra_1.0'] += 1
                        elif ra_value == '0.0':
                            stats[current_problem_type]['ra_0.0'] += 1
    
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return None
    
    return stats

def print_statistics(stats):
    """
    打印统计结果
    
    Args:
        stats (dict): 统计结果
    """
    if not stats:
        return
    
    print("=" * 60)
    print("统计结果")
    print("=" * 60)
    
    for problem_type in ['Easy', 'Hard']:
        print(f"\nProblem Type: {problem_type}")
        print("-" * 30)
        print(f"ra = 1.0 的样本数量: {stats[problem_type]['ra_1.0']}")
        print(f"ra = 0.0 的样本数量: {stats[problem_type]['ra_0.0']}")
        total = stats[problem_type]['ra_1.0'] + stats[problem_type]['ra_0.0']
        print(f"总计: {total}")
        
        if total > 0:
            ratio_1 = stats[problem_type]['ra_1.0'] / total * 100
            ratio_0 = stats[problem_type]['ra_0.0'] / total * 100
            print(f"ra = 1.0 占比: {ratio_1:.2f}%")
            print(f"ra = 0.0 占比: {ratio_0:.2f}%")
    
    # 总体统计
    print("\n" + "=" * 60)
    print("总体统计")
    print("=" * 60)
    
    total_easy = stats['Easy']['ra_1.0'] + stats['Easy']['ra_0.0']
    total_hard = stats['Hard']['ra_1.0'] + stats['Hard']['ra_0.0']
    total_ra_1 = stats['Easy']['ra_1.0'] + stats['Hard']['ra_1.0']
    total_ra_0 = stats['Easy']['ra_0.0'] + stats['Hard']['ra_0.0']
    grand_total = total_easy + total_hard
    
    print(f"Easy 问题总数: {total_easy}")
    print(f"Hard 问题总数: {total_hard}")
    print(f"ra = 1.0 总数: {total_ra_1}")
    print(f"ra = 0.0 总数: {total_ra_0}")
    print(f"总样本数: {grand_total}")
    
    if grand_total > 0:
        print(f"\nEasy 问题占比: {total_easy/grand_total*100:.2f}%")
        print(f"Hard 问题占比: {total_hard/grand_total*100:.2f}%")
        print(f"ra = 1.0 占比: {total_ra_1/grand_total*100:.2f}%")
        print(f"ra = 0.0 占比: {total_ra_0/grand_total*100:.2f}%")

def main():
    """主函数"""
    file_path = "/data/wuyang/R1-Omni-main/src/r1-v/logs/humanomni_emotion_emer_1format_withpath_withchoice_w5w5_easyhardtag_test4.txt"
    
    print("开始分析日志文件...")
    print(f"文件路径: {file_path}")
    
    stats = analyze_log_file(file_path)
    
    if stats:
        print_statistics(stats)
    else:
        print("分析失败，请检查文件路径和格式。")

if __name__ == "__main__":
    main()
