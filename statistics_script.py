#!/usr/bin/env python3
"""
统计文件中不同Problem Type (Easy/Hard) 中 ra=0.0 和 ra=1.0 的样本数量
"""

import re
from collections import defaultdict

def analyze_log_file(file_path):
    """
    分析日志文件，统计不同Problem Type中ra值的分布
    
    Args:
        file_path (str): 日志文件路径
    
    Returns:
        dict: 统计结果
    """
    
    # 初始化统计字典
    stats = {
        'Easy': {'ra_0.0': 0, 'ra_1.0': 0},
        'Hard': {'ra_0.0': 0, 'ra_1.0': 0}
    }
    
    current_problem_type = None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                
                # 检查Problem Type行
                if line.startswith('Problem Type:'):
                    if 'Easy' in line:
                        current_problem_type = 'Easy'
                    elif 'Hard' in line:
                        current_problem_type = 'Hard'
                
                # 检查包含ra值的行
                elif current_problem_type and 'ra=' in line:
                    # 使用正则表达式提取ra值
                    ra_match = re.search(r'ra=([\d.]+)', line)
                    if ra_match:
                        ra_value = float(ra_match.group(1))
                        
                        # 统计ra=0.0和ra=1.0的样本
                        if ra_value == 0.0:
                            stats[current_problem_type]['ra_0.0'] += 1
                        elif ra_value == 1.0:
                            stats[current_problem_type]['ra_1.0'] += 1
    
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        return None
    
    return stats

def print_statistics(stats):
    """
    打印统计结果
    
    Args:
        stats (dict): 统计结果字典
    """
    if stats is None:
        return
    
    print("=" * 60)
    print("Problem Type 和 ra 值统计结果")
    print("=" * 60)
    
    for problem_type in ['Easy', 'Hard']:
        print(f"\n{problem_type} 问题:")
        print(f"  ra=0.0 样本数量: {stats[problem_type]['ra_0.0']/8:,}")
        print(f"  ra=1.0 样本数量: {stats[problem_type]['ra_1.0']/8:,}")

        total = stats[problem_type]['ra_0.0'] + stats[problem_type]['ra_1.0']
        total = total/8
        print(f"  总计: {total:,}")

        if total > 0:
            ra_0_0_percent = (stats[problem_type]['ra_0.0']/8 / total) * 100
            ra_1_0_percent = (stats[problem_type]['ra_1.0']/8 / total) * 100
            print(f"  ra=0.0 占比: {ra_0_0_percent:.2f}%")
            print(f"  ra=1.0 占比: {ra_1_0_percent:.2f}%")
    
    # 总体统计
    total_easy = stats['Easy']['ra_0.0'] + stats['Easy']['ra_1.0']
    total_hard = stats['Hard']['ra_0.0'] + stats['Hard']['ra_1.0']
    grand_total = total_easy + total_hard

    print(f"\n总体统计:")
    print(f"  Easy 问题总数: {total_easy:,}")
    print(f"  Hard 问题总数: {total_hard:,}")
    print(f"  所有样本总数: {grand_total:,}")

    total_ra_0_0 = stats['Easy']['ra_0.0'] + stats['Hard']['ra_0.0']
    total_ra_1_0 = stats['Easy']['ra_1.0'] + stats['Hard']['ra_1.0']

    print(f"\n按 ra 值统计:")
    print(f"  ra=0.0 总数: {total_ra_0_0:,}")
    print(f"  ra=1.0 总数: {total_ra_1_0:,}")

    if grand_total > 0:
        print(f"  ra=0.0 总占比: {(total_ra_0_0 / grand_total) * 100:.2f}%")
        print(f"  ra=1.0 总占比: {(total_ra_1_0 / grand_total) * 100:.2f}%")

def main():
    """主函数"""
    file_path = "/data/wuyang/R1-Omni-main/src/r1-v/logs/humanomni_emotion_emer_1format_withpath_withchoice_w5w5_easyhardtag_test3.txt"
    
    print("开始分析日志文件...")
    print(f"文件路径: {file_path}")
    
    # 分析文件
    stats = analyze_log_file(file_path)
    
    # 打印结果
    print_statistics(stats)

if __name__ == "__main__":
    main()
