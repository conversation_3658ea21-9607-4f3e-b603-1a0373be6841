{"time":"2025-08-15T14:10:47.265164304+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-15T14:10:47.392604605+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-15T14:10:47.406428471+08:00","level":"INFO","msg":"stream: created new stream","id":"xwxweaij"}
{"time":"2025-08-15T14:10:47.406486908+08:00","level":"INFO","msg":"stream: started","id":"xwxweaij"}
{"time":"2025-08-15T14:10:47.406569137+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"xwxweaij"}
{"time":"2025-08-15T14:10:47.406600013+08:00","level":"INFO","msg":"handler: started","stream_id":"xwxweaij"}
{"time":"2025-08-15T14:10:47.406674517+08:00","level":"INFO","msg":"sender: started","stream_id":"xwxweaij"}
{"time":"2025-08-15T14:10:47.407562479+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-15T14:11:50.532069252+08:00","level":"INFO","msg":"stream: closing","id":"xwxweaij"}
{"time":"2025-08-15T14:11:50.532448908+08:00","level":"INFO","msg":"handler: closed","stream_id":"xwxweaij"}
{"time":"2025-08-15T14:11:50.532495102+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"xwxweaij"}
{"time":"2025-08-15T14:11:50.532511548+08:00","level":"INFO","msg":"sender: closed","stream_id":"xwxweaij"}
{"time":"2025-08-15T14:11:50.532657061+08:00","level":"INFO","msg":"stream: closed","id":"xwxweaij"}
