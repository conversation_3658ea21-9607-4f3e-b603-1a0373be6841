{"time":"2025-08-16T16:34:42.331806286+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T16:34:42.462953915+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T16:34:42.463187216+08:00","level":"INFO","msg":"stream: created new stream","id":"f17pc4gv"}
{"time":"2025-08-16T16:34:42.463221039+08:00","level":"INFO","msg":"stream: started","id":"f17pc4gv"}
{"time":"2025-08-16T16:34:42.463319424+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"f17pc4gv"}
{"time":"2025-08-16T16:34:42.463332903+08:00","level":"INFO","msg":"handler: started","stream_id":"f17pc4gv"}
{"time":"2025-08-16T16:34:42.463410647+08:00","level":"INFO","msg":"sender: started","stream_id":"f17pc4gv"}
{"time":"2025-08-16T16:34:42.464523139+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
