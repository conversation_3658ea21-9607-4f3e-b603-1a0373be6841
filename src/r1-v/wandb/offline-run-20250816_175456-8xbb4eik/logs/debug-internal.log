{"time":"2025-08-16T17:54:56.83898799+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T17:54:56.971455275+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T17:54:56.971697984+08:00","level":"INFO","msg":"stream: created new stream","id":"8xbb4eik"}
{"time":"2025-08-16T17:54:56.971730659+08:00","level":"INFO","msg":"stream: started","id":"8xbb4eik"}
{"time":"2025-08-16T17:54:56.971767516+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"8xbb4eik"}
{"time":"2025-08-16T17:54:56.971833575+08:00","level":"INFO","msg":"handler: started","stream_id":"8xbb4eik"}
{"time":"2025-08-16T17:54:56.97188337+08:00","level":"INFO","msg":"sender: started","stream_id":"8xbb4eik"}
{"time":"2025-08-16T17:54:56.972680456+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
