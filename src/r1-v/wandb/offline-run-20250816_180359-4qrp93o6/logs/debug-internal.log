{"time":"2025-08-16T18:04:00.176829835+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T18:04:00.312250476+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T18:04:00.312548917+08:00","level":"INFO","msg":"stream: created new stream","id":"4qrp93o6"}
{"time":"2025-08-16T18:04:00.312588284+08:00","level":"INFO","msg":"stream: started","id":"4qrp93o6"}
{"time":"2025-08-16T18:04:00.312686874+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"4qrp93o6"}
{"time":"2025-08-16T18:04:00.31274569+08:00","level":"INFO","msg":"sender: started","stream_id":"4qrp93o6"}
{"time":"2025-08-16T18:04:00.312802476+08:00","level":"INFO","msg":"handler: started","stream_id":"4qrp93o6"}
{"time":"2025-08-16T18:04:00.314197208+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
