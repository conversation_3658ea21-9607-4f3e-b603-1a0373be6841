{"time":"2025-08-16T17:07:26.046636449+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T17:07:26.176319521+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T17:07:26.176673262+08:00","level":"INFO","msg":"stream: created new stream","id":"u9gjha89"}
{"time":"2025-08-16T17:07:26.176719537+08:00","level":"INFO","msg":"stream: started","id":"u9gjha89"}
{"time":"2025-08-16T17:07:26.17682493+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"u9gjha89"}
{"time":"2025-08-16T17:07:26.17685679+08:00","level":"INFO","msg":"sender: started","stream_id":"u9gjha89"}
{"time":"2025-08-16T17:07:26.176902574+08:00","level":"INFO","msg":"handler: started","stream_id":"u9gjha89"}
{"time":"2025-08-16T17:07:26.178107142+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
