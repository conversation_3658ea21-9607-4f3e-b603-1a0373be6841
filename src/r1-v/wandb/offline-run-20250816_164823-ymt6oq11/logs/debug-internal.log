{"time":"2025-08-16T16:48:23.266781088+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T16:48:23.389039503+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T16:48:23.41045481+08:00","level":"INFO","msg":"stream: created new stream","id":"ymt6oq11"}
{"time":"2025-08-16T16:48:23.410495397+08:00","level":"INFO","msg":"stream: started","id":"ymt6oq11"}
{"time":"2025-08-16T16:48:23.410580845+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ymt6oq11"}
{"time":"2025-08-16T16:48:23.410605364+08:00","level":"INFO","msg":"sender: started","stream_id":"ymt6oq11"}
{"time":"2025-08-16T16:48:23.410642916+08:00","level":"INFO","msg":"handler: started","stream_id":"ymt6oq11"}
{"time":"2025-08-16T16:48:23.411792528+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
