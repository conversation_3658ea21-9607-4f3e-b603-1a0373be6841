{"time":"2025-08-16T14:47:17.927148257+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T14:47:18.058965127+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T14:47:18.059218969+08:00","level":"INFO","msg":"stream: created new stream","id":"81teivz5"}
{"time":"2025-08-16T14:47:18.059256378+08:00","level":"INFO","msg":"stream: started","id":"81teivz5"}
{"time":"2025-08-16T14:47:18.05935885+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"81teivz5"}
{"time":"2025-08-16T14:47:18.059419676+08:00","level":"INFO","msg":"handler: started","stream_id":"81teivz5"}
{"time":"2025-08-16T14:47:18.059483736+08:00","level":"INFO","msg":"sender: started","stream_id":"81teivz5"}
{"time":"2025-08-16T14:47:18.060537592+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-16T14:47:49.124817017+08:00","level":"INFO","msg":"stream: closing","id":"81teivz5"}
{"time":"2025-08-16T14:47:49.125756854+08:00","level":"INFO","msg":"handler: closed","stream_id":"81teivz5"}
{"time":"2025-08-16T14:47:49.125792377+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"81teivz5"}
{"time":"2025-08-16T14:47:49.125796351+08:00","level":"INFO","msg":"sender: closed","stream_id":"81teivz5"}
{"time":"2025-08-16T14:47:49.125857292+08:00","level":"INFO","msg":"stream: closed","id":"81teivz5"}
