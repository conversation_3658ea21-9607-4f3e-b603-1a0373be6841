{"time":"2025-08-16T16:12:40.488627545+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T16:12:40.614818779+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T16:12:40.61507306+08:00","level":"INFO","msg":"stream: created new stream","id":"yz90m8sx"}
{"time":"2025-08-16T16:12:40.615106438+08:00","level":"INFO","msg":"stream: started","id":"yz90m8sx"}
{"time":"2025-08-16T16:12:40.615214006+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"yz90m8sx"}
{"time":"2025-08-16T16:12:40.615291113+08:00","level":"INFO","msg":"handler: started","stream_id":"yz90m8sx"}
{"time":"2025-08-16T16:12:40.615360209+08:00","level":"INFO","msg":"sender: started","stream_id":"yz90m8sx"}
{"time":"2025-08-16T16:12:40.616132978+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
