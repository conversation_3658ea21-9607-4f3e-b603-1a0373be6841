{"time":"2025-08-16T16:31:22.458662749+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T16:31:22.58231111+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T16:31:22.582564651+08:00","level":"INFO","msg":"stream: created new stream","id":"uj52z3d9"}
{"time":"2025-08-16T16:31:22.582598677+08:00","level":"INFO","msg":"stream: started","id":"uj52z3d9"}
{"time":"2025-08-16T16:31:22.582721189+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"uj52z3d9"}
{"time":"2025-08-16T16:31:22.582784436+08:00","level":"INFO","msg":"sender: started","stream_id":"uj52z3d9"}
{"time":"2025-08-16T16:31:22.582854566+08:00","level":"INFO","msg":"handler: started","stream_id":"uj52z3d9"}
{"time":"2025-08-16T16:31:22.583964015+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
