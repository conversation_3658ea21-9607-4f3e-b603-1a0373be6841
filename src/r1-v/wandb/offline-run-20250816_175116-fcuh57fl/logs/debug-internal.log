{"time":"2025-08-16T17:51:17.035461502+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T17:51:17.172461927+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T17:51:17.1727823+08:00","level":"INFO","msg":"stream: created new stream","id":"fcuh57fl"}
{"time":"2025-08-16T17:51:17.172865951+08:00","level":"INFO","msg":"stream: started","id":"fcuh57fl"}
{"time":"2025-08-16T17:51:17.172941967+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"fcuh57fl"}
{"time":"2025-08-16T17:51:17.173054983+08:00","level":"INFO","msg":"sender: started","stream_id":"fcuh57fl"}
{"time":"2025-08-16T17:51:17.173112346+08:00","level":"INFO","msg":"handler: started","stream_id":"fcuh57fl"}
{"time":"2025-08-16T17:51:17.173816926+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-16T17:54:08.713198986+08:00","level":"INFO","msg":"stream: closing","id":"fcuh57fl"}
{"time":"2025-08-16T17:54:08.713541572+08:00","level":"INFO","msg":"handler: closed","stream_id":"fcuh57fl"}
{"time":"2025-08-16T17:54:08.713606567+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"fcuh57fl"}
{"time":"2025-08-16T17:54:08.713636853+08:00","level":"INFO","msg":"sender: closed","stream_id":"fcuh57fl"}
{"time":"2025-08-16T17:54:08.713738226+08:00","level":"INFO","msg":"stream: closed","id":"fcuh57fl"}
