{"time":"2025-08-16T15:54:18.619452064+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:54:18.753492107+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:54:18.75376558+08:00","level":"INFO","msg":"stream: created new stream","id":"euo274md"}
{"time":"2025-08-16T15:54:18.753800714+08:00","level":"INFO","msg":"stream: started","id":"euo274md"}
{"time":"2025-08-16T15:54:18.753885114+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"euo274md"}
{"time":"2025-08-16T15:54:18.753932343+08:00","level":"INFO","msg":"sender: started","stream_id":"euo274md"}
{"time":"2025-08-16T15:54:18.754101543+08:00","level":"INFO","msg":"handler: started","stream_id":"euo274md"}
{"time":"2025-08-16T15:54:18.755276924+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
