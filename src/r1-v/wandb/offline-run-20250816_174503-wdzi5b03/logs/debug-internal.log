{"time":"2025-08-16T17:45:03.287772642+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T17:45:03.421732947+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T17:45:03.42208905+08:00","level":"INFO","msg":"stream: created new stream","id":"wdzi5b03"}
{"time":"2025-08-16T17:45:03.422134564+08:00","level":"INFO","msg":"stream: started","id":"wdzi5b03"}
{"time":"2025-08-16T17:45:03.42223691+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wdzi5b03"}
{"time":"2025-08-16T17:45:03.422310003+08:00","level":"INFO","msg":"sender: started","stream_id":"wdzi5b03"}
{"time":"2025-08-16T17:45:03.422346588+08:00","level":"INFO","msg":"handler: started","stream_id":"wdzi5b03"}
{"time":"2025-08-16T17:45:03.42339803+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
