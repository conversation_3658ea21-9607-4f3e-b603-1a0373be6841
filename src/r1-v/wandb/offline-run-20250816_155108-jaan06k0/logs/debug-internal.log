{"time":"2025-08-16T15:51:09.059445411+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:51:09.182598347+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:51:09.182875877+08:00","level":"INFO","msg":"stream: created new stream","id":"jaan06k0"}
{"time":"2025-08-16T15:51:09.182917265+08:00","level":"INFO","msg":"stream: started","id":"jaan06k0"}
{"time":"2025-08-16T15:51:09.182978513+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"jaan06k0"}
{"time":"2025-08-16T15:51:09.183058065+08:00","level":"INFO","msg":"sender: started","stream_id":"jaan06k0"}
{"time":"2025-08-16T15:51:09.183147555+08:00","level":"INFO","msg":"handler: started","stream_id":"jaan06k0"}
{"time":"2025-08-16T15:51:09.183865291+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-16T15:51:36.233261531+08:00","level":"INFO","msg":"stream: closing","id":"jaan06k0"}
{"time":"2025-08-16T15:51:36.23356931+08:00","level":"INFO","msg":"handler: closed","stream_id":"jaan06k0"}
{"time":"2025-08-16T15:51:36.233595299+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"jaan06k0"}
{"time":"2025-08-16T15:51:36.233615167+08:00","level":"INFO","msg":"sender: closed","stream_id":"jaan06k0"}
{"time":"2025-08-16T15:51:36.233729027+08:00","level":"INFO","msg":"stream: closed","id":"jaan06k0"}
