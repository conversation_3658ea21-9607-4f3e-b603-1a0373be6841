{"time":"2025-08-16T17:13:44.247845811+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T17:13:44.379298133+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T17:13:44.379626523+08:00","level":"INFO","msg":"stream: created new stream","id":"ub3vi6li"}
{"time":"2025-08-16T17:13:44.379669301+08:00","level":"INFO","msg":"stream: started","id":"ub3vi6li"}
{"time":"2025-08-16T17:13:44.379698633+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ub3vi6li"}
{"time":"2025-08-16T17:13:44.379768972+08:00","level":"INFO","msg":"sender: started","stream_id":"ub3vi6li"}
{"time":"2025-08-16T17:13:44.379857072+08:00","level":"INFO","msg":"handler: started","stream_id":"ub3vi6li"}
{"time":"2025-08-16T17:13:44.380720873+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
