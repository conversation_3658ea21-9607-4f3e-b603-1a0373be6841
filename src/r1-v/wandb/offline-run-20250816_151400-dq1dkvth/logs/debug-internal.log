{"time":"2025-08-16T15:14:01.080412799+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:14:01.212235478+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:14:01.212474552+08:00","level":"INFO","msg":"stream: created new stream","id":"dq1dkvth"}
{"time":"2025-08-16T15:14:01.212507811+08:00","level":"INFO","msg":"stream: started","id":"dq1dkvth"}
{"time":"2025-08-16T15:14:01.212609244+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"dq1dkvth"}
{"time":"2025-08-16T15:14:01.212687987+08:00","level":"INFO","msg":"sender: started","stream_id":"dq1dkvth"}
{"time":"2025-08-16T15:14:01.212742565+08:00","level":"INFO","msg":"handler: started","stream_id":"dq1dkvth"}
{"time":"2025-08-16T15:14:01.213537633+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
