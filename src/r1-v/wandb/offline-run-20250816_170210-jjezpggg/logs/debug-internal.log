{"time":"2025-08-16T17:02:10.248430268+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T17:02:10.378678326+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T17:02:10.378908849+08:00","level":"INFO","msg":"stream: created new stream","id":"jjezpggg"}
{"time":"2025-08-16T17:02:10.37893904+08:00","level":"INFO","msg":"stream: started","id":"jjezpggg"}
{"time":"2025-08-16T17:02:10.379038853+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"jjezpggg"}
{"time":"2025-08-16T17:02:10.379083497+08:00","level":"INFO","msg":"handler: started","stream_id":"jjezpggg"}
{"time":"2025-08-16T17:02:10.379119214+08:00","level":"INFO","msg":"sender: started","stream_id":"jjezpggg"}
{"time":"2025-08-16T17:02:10.379730383+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
