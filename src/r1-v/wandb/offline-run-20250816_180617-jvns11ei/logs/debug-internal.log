{"time":"2025-08-16T18:06:17.596791792+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T18:06:17.726229941+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T18:06:17.726438289+08:00","level":"INFO","msg":"stream: created new stream","id":"jvns11ei"}
{"time":"2025-08-16T18:06:17.726470393+08:00","level":"INFO","msg":"stream: started","id":"jvns11ei"}
{"time":"2025-08-16T18:06:17.726548684+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"jvns11ei"}
{"time":"2025-08-16T18:06:17.726617314+08:00","level":"INFO","msg":"handler: started","stream_id":"jvns11ei"}
{"time":"2025-08-16T18:06:17.726634551+08:00","level":"INFO","msg":"sender: started","stream_id":"jvns11ei"}
{"time":"2025-08-16T18:06:17.727627397+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
