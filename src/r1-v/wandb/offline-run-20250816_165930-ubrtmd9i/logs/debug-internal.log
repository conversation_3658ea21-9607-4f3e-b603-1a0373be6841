{"time":"2025-08-16T16:59:30.752649398+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T16:59:30.876611515+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T16:59:30.876898738+08:00","level":"INFO","msg":"stream: created new stream","id":"ubrtmd9i"}
{"time":"2025-08-16T16:59:30.876933127+08:00","level":"INFO","msg":"stream: started","id":"ubrtmd9i"}
{"time":"2025-08-16T16:59:30.8770231+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ubrtmd9i"}
{"time":"2025-08-16T16:59:30.877054259+08:00","level":"INFO","msg":"sender: started","stream_id":"ubrtmd9i"}
{"time":"2025-08-16T16:59:30.877106238+08:00","level":"INFO","msg":"handler: started","stream_id":"ubrtmd9i"}
{"time":"2025-08-16T16:59:30.877720084+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
