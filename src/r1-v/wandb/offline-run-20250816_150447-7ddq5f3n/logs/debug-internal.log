{"time":"2025-08-16T15:04:47.81155048+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:04:47.938811973+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:04:47.939070706+08:00","level":"INFO","msg":"stream: created new stream","id":"7ddq5f3n"}
{"time":"2025-08-16T15:04:47.939109359+08:00","level":"INFO","msg":"stream: started","id":"7ddq5f3n"}
{"time":"2025-08-16T15:04:47.939208747+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"7ddq5f3n"}
{"time":"2025-08-16T15:04:47.93925318+08:00","level":"INFO","msg":"handler: started","stream_id":"7ddq5f3n"}
{"time":"2025-08-16T15:04:47.939336388+08:00","level":"INFO","msg":"sender: started","stream_id":"7ddq5f3n"}
{"time":"2025-08-16T15:04:47.939956325+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
