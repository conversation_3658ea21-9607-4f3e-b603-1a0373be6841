{"time":"2025-08-16T16:53:23.81372708+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T16:53:23.949920981+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T16:53:23.950268436+08:00","level":"INFO","msg":"stream: created new stream","id":"atevpvn8"}
{"time":"2025-08-16T16:53:23.950321736+08:00","level":"INFO","msg":"stream: started","id":"atevpvn8"}
{"time":"2025-08-16T16:53:23.950402052+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"atevpvn8"}
{"time":"2025-08-16T16:53:23.950486082+08:00","level":"INFO","msg":"sender: started","stream_id":"atevpvn8"}
{"time":"2025-08-16T16:53:23.950564536+08:00","level":"INFO","msg":"handler: started","stream_id":"atevpvn8"}
{"time":"2025-08-16T16:53:23.951526305+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
