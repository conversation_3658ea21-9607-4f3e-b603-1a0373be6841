{"time":"2025-08-16T17:38:08.216511525+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T17:38:08.353817209+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T17:38:08.354110209+08:00","level":"INFO","msg":"stream: created new stream","id":"vz3u4s48"}
{"time":"2025-08-16T17:38:08.354151986+08:00","level":"INFO","msg":"stream: started","id":"vz3u4s48"}
{"time":"2025-08-16T17:38:08.354192958+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"vz3u4s48"}
{"time":"2025-08-16T17:38:08.354214988+08:00","level":"INFO","msg":"sender: started","stream_id":"vz3u4s48"}
{"time":"2025-08-16T17:38:08.354311165+08:00","level":"INFO","msg":"handler: started","stream_id":"vz3u4s48"}
{"time":"2025-08-16T17:38:08.355294863+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
