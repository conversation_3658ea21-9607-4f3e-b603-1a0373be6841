{"time":"2025-08-16T15:39:24.73406817+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:39:24.859405972+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:39:24.859635775+08:00","level":"INFO","msg":"stream: created new stream","id":"530hy1oy"}
{"time":"2025-08-16T15:39:24.859669336+08:00","level":"INFO","msg":"stream: started","id":"530hy1oy"}
{"time":"2025-08-16T15:39:24.859702453+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"530hy1oy"}
{"time":"2025-08-16T15:39:24.859783344+08:00","level":"INFO","msg":"sender: started","stream_id":"530hy1oy"}
{"time":"2025-08-16T15:39:24.859778084+08:00","level":"INFO","msg":"handler: started","stream_id":"530hy1oy"}
{"time":"2025-08-16T15:39:24.860609348+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-16T15:39:52.924501322+08:00","level":"INFO","msg":"stream: closing","id":"530hy1oy"}
{"time":"2025-08-16T15:39:52.924762901+08:00","level":"INFO","msg":"handler: closed","stream_id":"530hy1oy"}
{"time":"2025-08-16T15:39:52.924786624+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"530hy1oy"}
{"time":"2025-08-16T15:39:52.924827587+08:00","level":"INFO","msg":"sender: closed","stream_id":"530hy1oy"}
{"time":"2025-08-16T15:39:52.924857727+08:00","level":"INFO","msg":"stream: closed","id":"530hy1oy"}
