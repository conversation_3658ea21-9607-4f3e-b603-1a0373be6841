{"time":"2025-08-16T15:36:01.415193073+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:36:01.546302963+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:36:01.546602074+08:00","level":"INFO","msg":"stream: created new stream","id":"yp9ejs6x"}
{"time":"2025-08-16T15:36:01.546685328+08:00","level":"INFO","msg":"stream: started","id":"yp9ejs6x"}
{"time":"2025-08-16T15:36:01.546675825+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"yp9ejs6x"}
{"time":"2025-08-16T15:36:01.546755983+08:00","level":"INFO","msg":"sender: started","stream_id":"yp9ejs6x"}
{"time":"2025-08-16T15:36:01.546775627+08:00","level":"INFO","msg":"handler: started","stream_id":"yp9ejs6x"}
{"time":"2025-08-16T15:36:01.547854404+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
