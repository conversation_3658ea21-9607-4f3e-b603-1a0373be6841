{"time":"2025-08-15T14:20:38.753423783+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-15T14:20:38.888188111+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-15T14:20:38.888568235+08:00","level":"INFO","msg":"stream: created new stream","id":"4y4a2gwy"}
{"time":"2025-08-15T14:20:38.888615207+08:00","level":"INFO","msg":"stream: started","id":"4y4a2gwy"}
{"time":"2025-08-15T14:20:38.88868562+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"4y4a2gwy"}
{"time":"2025-08-15T14:20:38.888806644+08:00","level":"INFO","msg":"handler: started","stream_id":"4y4a2gwy"}
{"time":"2025-08-15T14:20:38.888782648+08:00","level":"INFO","msg":"sender: started","stream_id":"4y4a2gwy"}
{"time":"2025-08-15T14:20:38.88978929+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-15T14:21:25.965407172+08:00","level":"INFO","msg":"stream: closing","id":"4y4a2gwy"}
{"time":"2025-08-15T14:21:25.965692841+08:00","level":"INFO","msg":"handler: closed","stream_id":"4y4a2gwy"}
{"time":"2025-08-15T14:21:25.965723696+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"4y4a2gwy"}
{"time":"2025-08-15T14:21:25.965740126+08:00","level":"INFO","msg":"sender: closed","stream_id":"4y4a2gwy"}
{"time":"2025-08-15T14:21:25.965828086+08:00","level":"INFO","msg":"stream: closed","id":"4y4a2gwy"}
