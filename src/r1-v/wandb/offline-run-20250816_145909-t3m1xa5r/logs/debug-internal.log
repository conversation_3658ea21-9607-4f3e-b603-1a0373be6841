{"time":"2025-08-16T14:59:09.526213735+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T14:59:09.656780612+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T14:59:09.656970705+08:00","level":"INFO","msg":"stream: created new stream","id":"t3m1xa5r"}
{"time":"2025-08-16T14:59:09.657006742+08:00","level":"INFO","msg":"stream: started","id":"t3m1xa5r"}
{"time":"2025-08-16T14:59:09.657091663+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"t3m1xa5r"}
{"time":"2025-08-16T14:59:09.65717403+08:00","level":"INFO","msg":"sender: started","stream_id":"t3m1xa5r"}
{"time":"2025-08-16T14:59:09.657242317+08:00","level":"INFO","msg":"handler: started","stream_id":"t3m1xa5r"}
{"time":"2025-08-16T14:59:09.658091329+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-16T14:59:43.727384303+08:00","level":"INFO","msg":"stream: closing","id":"t3m1xa5r"}
{"time":"2025-08-16T14:59:43.727720947+08:00","level":"INFO","msg":"handler: closed","stream_id":"t3m1xa5r"}
{"time":"2025-08-16T14:59:43.727810745+08:00","level":"INFO","msg":"sender: closed","stream_id":"t3m1xa5r"}
{"time":"2025-08-16T14:59:43.727813262+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"t3m1xa5r"}
{"time":"2025-08-16T14:59:43.728055641+08:00","level":"INFO","msg":"stream: closed","id":"t3m1xa5r"}
