{"time":"2025-08-16T15:01:26.770716593+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:01:26.906748433+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:01:26.907054279+08:00","level":"INFO","msg":"stream: created new stream","id":"580t8ury"}
{"time":"2025-08-16T15:01:26.907100501+08:00","level":"INFO","msg":"stream: started","id":"580t8ury"}
{"time":"2025-08-16T15:01:26.90718331+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"580t8ury"}
{"time":"2025-08-16T15:01:26.907206599+08:00","level":"INFO","msg":"handler: started","stream_id":"580t8ury"}
{"time":"2025-08-16T15:01:26.907335222+08:00","level":"INFO","msg":"sender: started","stream_id":"580t8ury"}
{"time":"2025-08-16T15:01:26.908368666+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
