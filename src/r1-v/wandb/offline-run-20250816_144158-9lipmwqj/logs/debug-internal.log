{"time":"2025-08-16T14:41:58.623907108+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T14:41:58.75006693+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T14:41:58.750354607+08:00","level":"INFO","msg":"stream: created new stream","id":"9lipmwqj"}
{"time":"2025-08-16T14:41:58.750394297+08:00","level":"INFO","msg":"stream: started","id":"9lipmwqj"}
{"time":"2025-08-16T14:41:58.750421756+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"9lipmwqj"}
{"time":"2025-08-16T14:41:58.750454973+08:00","level":"INFO","msg":"handler: started","stream_id":"9lipmwqj"}
{"time":"2025-08-16T14:41:58.750511877+08:00","level":"INFO","msg":"sender: started","stream_id":"9lipmwqj"}
{"time":"2025-08-16T14:41:58.751439154+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
