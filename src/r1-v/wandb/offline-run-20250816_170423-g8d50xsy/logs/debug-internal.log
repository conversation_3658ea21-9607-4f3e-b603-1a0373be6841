{"time":"2025-08-16T17:04:23.692569766+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T17:04:23.819121188+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T17:04:23.819387959+08:00","level":"INFO","msg":"stream: created new stream","id":"g8d50xsy"}
{"time":"2025-08-16T17:04:23.819425514+08:00","level":"INFO","msg":"stream: started","id":"g8d50xsy"}
{"time":"2025-08-16T17:04:23.819505739+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"g8d50xsy"}
{"time":"2025-08-16T17:04:23.819604521+08:00","level":"INFO","msg":"handler: started","stream_id":"g8d50xsy"}
{"time":"2025-08-16T17:04:23.819625822+08:00","level":"INFO","msg":"sender: started","stream_id":"g8d50xsy"}
{"time":"2025-08-16T17:04:23.820442341+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
