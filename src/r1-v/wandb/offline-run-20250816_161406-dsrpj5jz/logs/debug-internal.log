{"time":"2025-08-16T16:14:07.079597077+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T16:14:07.213877227+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T16:14:07.21412263+08:00","level":"INFO","msg":"stream: created new stream","id":"dsrpj5jz"}
{"time":"2025-08-16T16:14:07.214160041+08:00","level":"INFO","msg":"stream: started","id":"dsrpj5jz"}
{"time":"2025-08-16T16:14:07.21425914+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"dsrpj5jz"}
{"time":"2025-08-16T16:14:07.214340617+08:00","level":"INFO","msg":"sender: started","stream_id":"dsrpj5jz"}
{"time":"2025-08-16T16:14:07.21449532+08:00","level":"INFO","msg":"handler: started","stream_id":"dsrpj5jz"}
{"time":"2025-08-16T16:14:07.215511232+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
