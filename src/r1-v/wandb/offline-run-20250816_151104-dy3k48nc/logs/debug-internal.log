{"time":"2025-08-16T15:11:04.603164026+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:11:04.726362004+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:11:04.726595372+08:00","level":"INFO","msg":"stream: created new stream","id":"dy3k48nc"}
{"time":"2025-08-16T15:11:04.726630389+08:00","level":"INFO","msg":"stream: started","id":"dy3k48nc"}
{"time":"2025-08-16T15:11:04.726713574+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"dy3k48nc"}
{"time":"2025-08-16T15:11:04.726766359+08:00","level":"INFO","msg":"handler: started","stream_id":"dy3k48nc"}
{"time":"2025-08-16T15:11:04.726827663+08:00","level":"INFO","msg":"sender: started","stream_id":"dy3k48nc"}
{"time":"2025-08-16T15:11:04.727682538+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
