{"time":"2025-08-16T16:35:56.377406122+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T16:35:56.507290657+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T16:35:56.507753206+08:00","level":"INFO","msg":"stream: created new stream","id":"wusxw3jm"}
{"time":"2025-08-16T16:35:56.507791337+08:00","level":"INFO","msg":"stream: started","id":"wusxw3jm"}
{"time":"2025-08-16T16:35:56.507864271+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wusxw3jm"}
{"time":"2025-08-16T16:35:56.507960242+08:00","level":"INFO","msg":"sender: started","stream_id":"wusxw3jm"}
{"time":"2025-08-16T16:35:56.508091588+08:00","level":"INFO","msg":"handler: started","stream_id":"wusxw3jm"}
{"time":"2025-08-16T16:35:56.5087842+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-16T16:45:32.203264812+08:00","level":"INFO","msg":"stream: closing","id":"wusxw3jm"}
{"time":"2025-08-16T16:45:32.203658383+08:00","level":"INFO","msg":"handler: closed","stream_id":"wusxw3jm"}
{"time":"2025-08-16T16:45:32.203691241+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"wusxw3jm"}
{"time":"2025-08-16T16:45:32.20370332+08:00","level":"INFO","msg":"sender: closed","stream_id":"wusxw3jm"}
{"time":"2025-08-16T16:45:32.203823315+08:00","level":"INFO","msg":"stream: closed","id":"wusxw3jm"}
