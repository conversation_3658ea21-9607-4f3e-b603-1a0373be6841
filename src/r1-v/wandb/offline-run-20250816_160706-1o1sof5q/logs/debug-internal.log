{"time":"2025-08-16T16:07:06.37340466+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T16:07:06.507644063+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T16:07:06.507880964+08:00","level":"INFO","msg":"stream: created new stream","id":"1o1sof5q"}
{"time":"2025-08-16T16:07:06.507913573+08:00","level":"INFO","msg":"stream: started","id":"1o1sof5q"}
{"time":"2025-08-16T16:07:06.507939071+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"1o1sof5q"}
{"time":"2025-08-16T16:07:06.508067457+08:00","level":"INFO","msg":"sender: started","stream_id":"1o1sof5q"}
{"time":"2025-08-16T16:07:06.508110891+08:00","level":"INFO","msg":"handler: started","stream_id":"1o1sof5q"}
{"time":"2025-08-16T16:07:06.508775986+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-16T16:07:31.56857173+08:00","level":"INFO","msg":"stream: closing","id":"1o1sof5q"}
{"time":"2025-08-16T16:07:31.568929653+08:00","level":"INFO","msg":"handler: closed","stream_id":"1o1sof5q"}
{"time":"2025-08-16T16:07:31.568967693+08:00","level":"INFO","msg":"sender: closed","stream_id":"1o1sof5q"}
{"time":"2025-08-16T16:07:31.568967132+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"1o1sof5q"}
{"time":"2025-08-16T16:07:31.569123363+08:00","level":"INFO","msg":"stream: closed","id":"1o1sof5q"}
