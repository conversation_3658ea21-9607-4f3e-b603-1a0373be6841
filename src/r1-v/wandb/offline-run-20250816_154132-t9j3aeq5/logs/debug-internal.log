{"time":"2025-08-16T15:41:33.056637384+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:41:33.189883162+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:41:33.19011807+08:00","level":"INFO","msg":"stream: created new stream","id":"t9j3aeq5"}
{"time":"2025-08-16T15:41:33.19015047+08:00","level":"INFO","msg":"stream: started","id":"t9j3aeq5"}
{"time":"2025-08-16T15:41:33.190282573+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"t9j3aeq5"}
{"time":"2025-08-16T15:41:33.19032658+08:00","level":"INFO","msg":"sender: started","stream_id":"t9j3aeq5"}
{"time":"2025-08-16T15:41:33.190412788+08:00","level":"INFO","msg":"handler: started","stream_id":"t9j3aeq5"}
{"time":"2025-08-16T15:41:33.191427571+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
