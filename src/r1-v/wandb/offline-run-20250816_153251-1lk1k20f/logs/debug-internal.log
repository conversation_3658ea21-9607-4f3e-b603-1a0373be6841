{"time":"2025-08-16T15:32:51.354299483+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:32:51.485663628+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:32:51.485865284+08:00","level":"INFO","msg":"stream: created new stream","id":"1lk1k20f"}
{"time":"2025-08-16T15:32:51.485892509+08:00","level":"INFO","msg":"stream: started","id":"1lk1k20f"}
{"time":"2025-08-16T15:32:51.485941759+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"1lk1k20f"}
{"time":"2025-08-16T15:32:51.486069596+08:00","level":"INFO","msg":"handler: started","stream_id":"1lk1k20f"}
{"time":"2025-08-16T15:32:51.486069372+08:00","level":"INFO","msg":"sender: started","stream_id":"1lk1k20f"}
{"time":"2025-08-16T15:32:51.48723151+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-16T15:33:24.5563011+08:00","level":"INFO","msg":"stream: closing","id":"1lk1k20f"}
{"time":"2025-08-16T15:33:24.556623535+08:00","level":"INFO","msg":"handler: closed","stream_id":"1lk1k20f"}
{"time":"2025-08-16T15:33:24.556661224+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"1lk1k20f"}
{"time":"2025-08-16T15:33:24.55721635+08:00","level":"INFO","msg":"sender: closed","stream_id":"1lk1k20f"}
{"time":"2025-08-16T15:33:24.557257089+08:00","level":"INFO","msg":"stream: closed","id":"1lk1k20f"}
