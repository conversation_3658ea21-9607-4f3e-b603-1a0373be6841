{"time":"2025-08-16T17:42:18.766188838+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T17:42:18.90264966+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T17:42:18.903008209+08:00","level":"INFO","msg":"stream: created new stream","id":"ezm96pnf"}
{"time":"2025-08-16T17:42:18.903058907+08:00","level":"INFO","msg":"stream: started","id":"ezm96pnf"}
{"time":"2025-08-16T17:42:18.903149913+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ezm96pnf"}
{"time":"2025-08-16T17:42:18.903174076+08:00","level":"INFO","msg":"handler: started","stream_id":"ezm96pnf"}
{"time":"2025-08-16T17:42:18.903164798+08:00","level":"INFO","msg":"sender: started","stream_id":"ezm96pnf"}
{"time":"2025-08-16T17:42:18.904284842+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-16T17:42:43.954401283+08:00","level":"INFO","msg":"stream: closing","id":"ezm96pnf"}
{"time":"2025-08-16T17:42:43.954663276+08:00","level":"INFO","msg":"handler: closed","stream_id":"ezm96pnf"}
{"time":"2025-08-16T17:42:43.954694697+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"ezm96pnf"}
{"time":"2025-08-16T17:42:43.954790746+08:00","level":"INFO","msg":"sender: closed","stream_id":"ezm96pnf"}
{"time":"2025-08-16T17:42:43.954839872+08:00","level":"INFO","msg":"stream: closed","id":"ezm96pnf"}
