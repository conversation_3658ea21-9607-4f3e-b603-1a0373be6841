{"time":"2025-08-16T18:01:18.160303639+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T18:01:18.295653866+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T18:01:18.295943225+08:00","level":"INFO","msg":"stream: created new stream","id":"mdqz2xmy"}
{"time":"2025-08-16T18:01:18.295989713+08:00","level":"INFO","msg":"stream: started","id":"mdqz2xmy"}
{"time":"2025-08-16T18:01:18.296121716+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"mdqz2xmy"}
{"time":"2025-08-16T18:01:18.296176573+08:00","level":"INFO","msg":"handler: started","stream_id":"mdqz2xmy"}
{"time":"2025-08-16T18:01:18.296227352+08:00","level":"INFO","msg":"sender: started","stream_id":"mdqz2xmy"}
{"time":"2025-08-16T18:01:18.297483037+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
