------------- 17-00-39-19-374870 Three-Level Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Difficulty Distribution: Easy=0, Medium=4, Hard=0
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([False], device='cuda:0'), 'audio_anomaly': tensor([False], device='cuda:0'), 'visual_boost': tensor([-0.0117], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.0469], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.9961], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9844], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9492], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, level=Medium, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, level=Medium, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, level=Medium, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, level=Medium, reward=0.018
------------- 17-00-39-36-701198 Three-Level Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Difficulty Distribution: Easy=0, Medium=0, Hard=4
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([True], device='cuda:0'), 'audio_anomaly': tensor([True], device='cuda:0'), 'visual_boost': tensor([0.1953], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.1953], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.8008], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9961], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9961], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, level=Hard, reward=-0.007
Completion 1: length=903, L/Lavg=0.994, ra=0.0, level=Hard, reward=-0.006
Completion 2: length=734, L/Lavg=0.808, ra=0.0, level=Hard, reward=-0.192
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, level=Hard, reward=-0.102
------------- 17-00-39-50-806851 Three-Level Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Difficulty Distribution: Easy=0, Medium=0, Hard=4
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([False], device='cuda:0'), 'visual_anomaly': tensor([True], device='cuda:0'), 'audio_anomaly': tensor([True], device='cuda:0'), 'visual_boost': tensor([0.2188], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.2188], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.7812], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([1.], device='cuda:0', dtype=torch.bfloat16, grad_fn=<StackBackward0>), 'audio': tensor([1.], device='cuda:0', dtype=torch.bfloat16, grad_fn=<StackBackward0>)}}
Average Length (Lavg): 894.25
Completion 0: length=1343, L/Lavg=1.502, ra=0.0, level=Hard, reward=0.500
Completion 1: length=789, L/Lavg=0.882, ra=1.0, level=Hard, reward=0.059
Completion 2: length=659, L/Lavg=0.737, ra=1.0, level=Hard, reward=0.132
Completion 3: length=786, L/Lavg=0.879, ra=0.0, level=Hard, reward=-0.121
------------- 17-00-41-46-533382 Three-Level Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Difficulty Distribution: Easy=0, Medium=1, Hard=0
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([False], device='cuda:0'), 'audio_anomaly': tensor([False], device='cuda:0'), 'visual_boost': tensor([-0.0117], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.0469], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.9961], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9844], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9492], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, level=Medium, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, level=Medium, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, level=Medium, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, level=Medium, reward=0.018
------------- 17-00-42-03-549007 Three-Level Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Difficulty Distribution: Easy=0, Medium=0, Hard=1
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([True], device='cuda:0'), 'audio_anomaly': tensor([True], device='cuda:0'), 'visual_boost': tensor([0.1953], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.1953], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.8008], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9961], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9961], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, level=Hard, reward=-0.007
Completion 1: length=903, L/Lavg=0.994, ra=0.0, level=Hard, reward=-0.006
Completion 2: length=734, L/Lavg=0.808, ra=0.0, level=Hard, reward=-0.192
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, level=Hard, reward=-0.102
