# Copyright 2025 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import logging
import os
import re
import json
from datetime import datetime
from dataclasses import dataclass, field
from typing import Optional

from datasets import load_dataset, load_from_disk, Features,Sequence,Value
from transformers import Qwen2VLForConditionalGeneration

from math_verify import parse, verify
from open_r1.trainer import Qwen2V<PERSON>GRPOTrainer, Qwen2VLGRPOVLLMTrainer, HumanOmniVLGRPOTrainer
from trl import GRPOConfig, GRPOTrainer, ModelConfig, ScriptArguments, Trl<PERSON>arser, get_peft_config


@dataclass
class GRPOScriptArguments(ScriptArguments):
    """
    Script arguments for the GRPO training script.

    Args:
        reward_funcs (`list[str]`):
            List of reward functions. Possible values: 'accuracy', 'format', 'length'.
    """

    reward_funcs: list[str] = field(
        default_factory=lambda: ["accuracy","format","new_length"],
        metadata={"help": "List of reward functions. Possible values: 'accuracy', 'format', 'length'"},
    )
    # accuracy_threshold: float = field(
    #     default=0.5,
    #     metadata={"help": "Accuracy threshold for length reward. When group accuracy > threshold, length reward is applied."},
    # )
    max_pixels: Optional[int] = field(
        default=12845056,
        metadata={"help": "Maximum number of pixels for the image"},
    )
    min_pixels: Optional[int] = field(
        default=3136,
        metadata={"help": "Minimum number of pixels for the image"},
    )


def accuracy_reward(completions, solution, **kwargs):
    """Reward function that checks if the completion is correct using either symbolic verification or exact string matching."""
    contents = [completion[0]["content"] for completion in completions]
    rewards = []
    videos = kwargs.get("video", "")
    current_time = datetime.now().strftime("%d-%H-%M-%S-%f")
    for video, content, sol in zip(videos, contents, solution):
        reward = 0.0
        # Try symbolic verification first
        try:
            answer = parse(content)
            if float(verify(answer, parse(sol))) > 0:
                reward = 1.0
        except Exception:
            pass  # Continue to next verification method if this fails

        # If symbolic verification failed, try string matching
        if reward == 0.0:
            try:
                # Extract answer from solution if it has think/answer tags
                sol_match = re.search(r'<answer>(.*?)</answer>', sol)
                ground_truth = sol_match.group(1).strip() if sol_match else sol.strip()
                
                # Extract answer from content if it has think/answer tags
                content_match = re.search(r'<answer>(.*?)</answer>', content)
                student_answer = content_match.group(1).strip() if content_match else content.strip()
                
                # Compare the extracted answers
                if student_answer == ground_truth:
                    reward = 1.0
            except Exception:
                pass  # Keep reward as 0.0 if both methods fail
                
        rewards.append(reward)
        if os.getenv("DEBUG_MODE") == "truee":
            log_path = os.getenv("LOG_PATH")
            # local_rank = int(os.getenv("LOCAL_RANK", 0))
            with open(log_path, "a") as f:
                f.write(f"------------- {current_time} Accuracy reward: {reward} -------------\n")
                f.write(f"Video: {video}\n")
                f.write(f"Content: {content}\n")
                f.write(f"Solution: {sol}\n")
    return rewards


def format_reward(completions, **kwargs):
    """Reward function that checks if the completion has a specific format."""
    pattern = r"<think>.*?</think>\s*<answer>.*?</answer>"
    completion_contents = [completion[0]["content"] for completion in completions]
    matches = [re.fullmatch(pattern, content, re.DOTALL) for content in completion_contents]
    return [1.0 if match else 0.0 for match in matches]

def format_reward_2(completions, **kwargs):
    """Reward function that checks if the completion has a specific format."""
    pattern = r"<think>.*?</think>\s*<answer>.*?</answer>"
    completion_contents = [completion[0]["content"] for completion in completions]
    matches = [re.fullmatch(pattern, content, re.DOTALL) for content in completion_contents]
    return [1.0 if match else 0.0 for match in matches]


def length_reward(completions, solution, difficulty_metrics, **kwargs):
    """
    Difficulty-aware Length Reward function.

    Encourages concise responses only for easy problems (when group accuracy > threshold).
    For hard problems, this reward is 0 to maintain long thinking capabilities.

    Formula when easy (ti = [Easy]):
    rl(yi) = 1.0 - (1 - cos((li_j / Li) * π))

    Args:
        completions: List of model completions
        solution: List of ground truth solutions
        accuracy_threshold: Threshold to determine if problem is "easy" (default: 0.5)
        **kwargs: Additional arguments

    Returns:
        List of length rewards for each completion
    """
    import math

    # # First, calculate accuracy rewards to determine if this is an "easy" group
    # accuracy_rewards = accuracy_reward(completions, solution, **kwargs)
    # group_accuracy = sum(accuracy_rewards) / len(accuracy_rewards) if accuracy_rewards else 0.0

    # If group accuracy is below threshold, return 0 rewards (hard problem)
    if difficulty_metrics["difficulty_score"]:
        length_rewards =  [0.0] * len(completions)
    else:
        # Extract completion contents and calculate lengths
        contents = [completion[0]["content"] for completion in completions]
        lengths = [len(content) for content in contents]
        max_length = max(lengths) if lengths else 1  # Li: max length in the group

        # Calculate length rewards for easy problems
        length_rewards = []
        for length in lengths:
            # li_j / Li ratio
            length_ratio = length / max_length
            # Apply cosine-based formula: 1.0 - (1 - cos((li_j / Li) * π))
            cos_term = math.cos(length_ratio * math.pi)
            reward = 1.0 - (1.0 - cos_term)/2
            length_rewards.append(reward)

    # Debug logging if enabled
    if os.getenv("DEBUG_MODE") == "true":
        log_path = os.getenv("LOG_PATH")
        current_time = datetime.now().strftime("%d-%H-%M-%S-%f")
        with open(log_path, "a") as f:
            f.write(f"------------- {current_time} Length Reward Debug -------------\n")

            # Difficulty metrics logging
            difficulty_score = difficulty_metrics["difficulty_score"]
            modal_conflict = difficulty_metrics.get("modal_conflict", [False])[0] if isinstance(difficulty_metrics.get("modal_conflict"), list) else difficulty_metrics.get("modal_conflict", False)
            visual_anomaly = difficulty_metrics.get("visual_anomaly", [False])[0] if isinstance(difficulty_metrics.get("visual_anomaly"), list) else difficulty_metrics.get("visual_anomaly", False)
            audio_anomaly = difficulty_metrics.get("audio_anomaly", [False])[0] if isinstance(difficulty_metrics.get("audio_anomaly"), list) else difficulty_metrics.get("audio_anomaly", False)

            f.write(f"Difficulty Score: {difficulty_score} (0=Easy, 1=Hard)\n")
            f.write(f"Problem Type: {'Hard' if difficulty_score else 'Easy'}\n")
            f.write(f"Modal Conflict: {modal_conflict}\n")
            f.write(f"Visual Anomaly: {visual_anomaly}\n")
            f.write(f"Audio Anomaly: {audio_anomaly}\n")

            # Confidence metrics if available
            if "confidences" in difficulty_metrics:
                confidences = difficulty_metrics["confidences"]

                # Convert tensor confidences to float if needed
                full_conf = confidences.get('full', 'N/A')
                visual_conf = confidences.get('visual', 'N/A')
                audio_conf = confidences.get('audio', 'N/A')

                if hasattr(full_conf, 'item'):
                    full_conf = full_conf.item()
                if hasattr(visual_conf, 'item'):
                    visual_conf = visual_conf.item()
                if hasattr(audio_conf, 'item'):
                    audio_conf = audio_conf.item()

                f.write(f"Full Modal Confidence: {full_conf}\n")
                f.write(f"Visual Only Confidence: {visual_conf}\n")
                f.write(f"Audio Only Confidence: {audio_conf}\n")

            # Confidence boost metrics if available
            visual_boost = difficulty_metrics.get("visual_boost", [0])[0] if isinstance(difficulty_metrics.get("visual_boost"), list) else difficulty_metrics.get("visual_boost", 0)
            audio_boost = difficulty_metrics.get("audio_boost", [0])[0] if isinstance(difficulty_metrics.get("audio_boost"), list) else difficulty_metrics.get("audio_boost", 0)

            # Convert tensor to float if needed
            if hasattr(visual_boost, 'item'):
                visual_boost = visual_boost.item()
            if hasattr(audio_boost, 'item'):
                audio_boost = audio_boost.item()

            f.write(f"Visual Confidence Boost: {visual_boost:.3f}\n")
            f.write(f"Audio Confidence Boost: {audio_boost:.3f}\n")

            if difficulty_metrics["difficulty_score"]:
                f.write(f"Hard samples all zeros :{length_rewards}\n")
            else:
                f.write(f"Max Length: {max_length}\n")
                for i, (length, reward) in enumerate(zip(lengths, length_rewards)):
                    f.write(f"Completion {i}: length={length}, ratio={length/max_length:.3f}, reward={reward:.3f}\n")

    return length_rewards


# Global variable to store lambda value across calls
_lambda_t = 1e-4

def direct_length_reward(completions, solution, difficulty, accuracy_threshold=0.9, min_length=400, **kwargs):
    """
    Adaptive Length Penalty Reward function based on the formula:
    λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
    R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y) - penalty_for_short_answers

    Args:
        completions: List of model completions
        solution: List of ground truth solutions
        accuracy_threshold: Reference accuracy (acc_ref), default=1
        min_length: Minimum required length for completions, default=10
        **kwargs: Additional arguments

    Returns:
        List of length rewards for each completion
    """
    global _lambda_t

    # Hyperparameters
    eta = 1e-4  # Learning rate for length penalty update
    acc_ref = accuracy_threshold  # Reference accuracy

    # Calculate accuracy rewards
    accuracy_rewards = accuracy_reward(completions, solution, **kwargs)
    group_accuracy = sum(accuracy_rewards) / len(accuracy_rewards) if accuracy_rewards else 0.0

    # Update lambda using the adaptive formula
    # λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
    lambda_old = _lambda_t
    _lambda_t = max(0, _lambda_t + eta * (group_accuracy - acc_ref))

    # Extract completion contents and calculate lengths
    contents = [completion[0]["content"] for completion in completions]
    lengths = [len(content) for content in contents]

    # Calculate length rewards using the formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y) - penalty_for_short_answers
    length_rewards = []
    for i, length in enumerate(lengths):
        ra = accuracy_rewards[i]  # Individual accuracy (𝟙{y = y*})
        base_reward = ra - _lambda_t * length  # R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
        
        # Add penalty for answers shorter than minimum length
        if length < min_length:
            short_penalty = 0.5 * (min_length - length) / min_length  # Proportional penalty
            reward = base_reward - short_penalty
        else:
            reward = base_reward
            
        length_rewards.append(reward)

    # Debug logging if enabled
    if os.getenv("DEBUG_MODE") == "true":
        log_path = os.getenv("LOG_PATH")
        current_time = datetime.now().strftime("%d-%H-%M-%S-%f")
        with open(log_path, "a") as f:
            f.write(f"------------- {current_time} Adaptive Length Reward Debug -------------\n")
            f.write(f"Problem Type: {difficulty}\n")
            f.write(f"Group Accuracy (acc_t): {group_accuracy:.6f}\n")
            f.write(f"Reference Accuracy (acc_ref): {acc_ref:.6f}\n")
            f.write(f"Learning Rate (η): {eta:.6f}\n")
            f.write(f"Lambda Update: λ_old={lambda_old:.6f} -> λ_new={_lambda_t:.6f}\n")
            f.write(f"Lambda Change: Δλ = {eta * (group_accuracy - acc_ref):.6f}\n")
            f.write(f"Formula: R_λ(x, y) = 𝟙{{y = y*}} - λ_t · len(y)\n")
            f.write(f"Lambda Update: λ_{{t+1}} = max(0, λ_t + η · (acc_t - acc_ref))\n")
            f.write(f"Minimum Length Constraint: {min_length}\n")
            for i, (length, final_reward, acc) in enumerate(zip(lengths, length_rewards, accuracy_rewards)):
                f.write(f"Completion {i}: length={length}, accuracy={acc}\n")
                if length < min_length:
                    short_penalty = 0.5 * (min_length - length) / min_length
                    f.write(f"  -> Calculation: {acc} - {_lambda_t:.6f} * {length} - {short_penalty:.6f} = {final_reward:.6f}\n")
                else:
                    f.write(f"  -> Calculation: {acc} - {_lambda_t:.6f} * {length} = {final_reward:.6f}\n")

    return length_rewards


def length_reward_difficulty(completions, solution, difficulty, **kwargs):
    """
    Difficulty-aware Length Reward function.

    Encourages concise responses only for easy problems (when group accuracy > threshold).
    For hard problems, this reward is 0 to maintain long thinking capabilities.

    Formula when easy (ti = [Easy]):
    rl(yi) = 1.0 - (1 - cos((li_j / Li) * π))

    Args:
        completions: List of model completions
        solution: List of ground truth solutions
        accuracy_threshold: Threshold to determine if problem is "easy" (default: 0.5)
        **kwargs: Additional arguments

    Returns:
        List of length rewards for each completion
    """
    import math
    # If group accuracy is below threshold, return 0 rewards (hard problem)
    if difficulty[0] == "hard":
        return [0.0] * len(completions)
    else:
        # Extract completion contents and calculate lengths
        contents = [completion[0]["content"] for completion in completions]
        lengths = [len(content) for content in contents]
        max_length = max(lengths) if lengths else 1  # Li: max length in the group

        # Calculate length rewards for easy problems
        length_rewards = []
        for length in lengths:
            # li_j / Li ratio
            length_ratio = length / max_length
            # Apply cosine-based formula: 1.0 - (1 - cos((li_j / Li) * π))
            cos_term = math.cos(length_ratio * math.pi)
            reward = 1.0 - (1.0 - cos_term)/2
            length_rewards.append(reward)

        # Debug logging if enabled
        if os.getenv("DEBUG_MODE") == "true":
            log_path = os.getenv("LOG_PATH")
            current_time = datetime.now().strftime("%d-%H-%M-%S-%f")
            with open(log_path, "a") as f:
                f.write(f"------------- {current_time} Length Reward Debug -------------\n")
                f.write(f"Problem Type: {difficulty}\n")
                f.write(f"Max Length: {max_length}\n")
                for i, (length, reward) in enumerate(zip(lengths, length_rewards)):
                    f.write(f"Completion {i}: length={length}, ratio={length/max_length:.3f}, reward={reward:.3f}\n")

    return length_rewards


def new_length_reward(completions, solution, difficulty, difficulty_metrics, accuracy_threshold=0.6, **kwargs):
    """
    New length reward function based on the provided formula with accuracy threshold.

    Formula:
    rt = {
        1 - L/Lavg           if group_accuracy >= threshold and ra = 1 (easy)
        min(L/Lavg - 1, 1)   if group_accuracy < threshold and ra = 0 (hard)
        0                    otherwise
    }

    Args:
        completions: List of model completions
        solution: List of ground truth solutions
        difficulty: Problem difficulty ("easy" or "hard") - now replaced by accuracy threshold
        accuracy_threshold: Threshold to determine if problem is "easy" (default: 0.5)
        **kwargs: Additional arguments

    Returns:
        List of length rewards for each completion
    """

    # print(difficulty_metrics)
    # Calculate accuracy rewards for each completion
    accuracy_rewards = accuracy_reward(completions, solution, **kwargs)
    group_accuracy = sum(accuracy_rewards) / len(accuracy_rewards) if accuracy_rewards else 0.0

    # Determine difficulty based on group accuracy threshold
    # is_easy = group_accuracy >= accuracy_threshold
    is_easy = 1 - difficulty_metrics["difficulty_score"]  # 反转：1->0, 0->1
    # Extract completion contents and calculate lengths
    contents = [completion[0]["content"] for completion in completions]
    lengths = [len(content) for content in contents]
    avg_length = sum(lengths) / len(lengths) if lengths else 1  # Lavg: average length in the group

    # Calculate length rewards based on the formula
    length_rewards = []
    for i, length in enumerate(lengths):
        L = length
        Lavg = avg_length
        ra = accuracy_rewards[i]  # Individual accuracy for this completion

        if is_easy and ra == 1:
            # Case 1: 1 - L/Lavg (easy problem with correct answer)
            reward = 1 - (L / Lavg)
        elif not is_easy and ra == 0:
            # Case 2: min(L/Lavg - 1, 1) (hard problem with incorrect answer)
            reward = min((L / Lavg) - 1, 1)
        else:
            # Case 3: otherwise
            reward = 0

        length_rewards.append(reward)

    # Debug logging if enabled
    if os.getenv("DEBUG_MODE") == "true":
        log_path = os.getenv("LOG_PATH")
        current_time = datetime.now().strftime("%d-%H-%M-%S-%f")
        with open(log_path, "a") as f:
            f.write(f"------------- {current_time} New Length Reward Debug -------------\n")
            f.write(f"Group Accuracy: {group_accuracy:.3f} (threshold: {accuracy_threshold})\n")
            f.write(f"Problem Type: {'Easy' if is_easy else 'Hard'} \n")
            f.write(f"Original Difficulty: {difficulty}\n")
            f.write(f"{difficulty_metrics}\n")
            f.write(f"Average Length (Lavg): {avg_length}\n")
            for i, (length, reward, acc) in enumerate(zip(lengths, length_rewards, accuracy_rewards)):
                f.write(f"Completion {i}: length={length}, L/Lavg={length/avg_length:.3f}, ra={acc}, reward={reward:.3f}\n")

    return length_rewards

reward_funcs_registry = {
    "accuracy": accuracy_reward,
    "format": format_reward,
    "length": length_reward, #3.1
    "length_diff": length_reward_difficulty, #3.2
    "new_length": new_length_reward,#3.3
    "direct_length": direct_length_reward, #加了最低长度限制
}

SYSTEM_PROMPT = (
    "A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant "
    "first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning "
    "process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., "
    "<think> reasoning process here </think><answer> answer here </answer>"
)

from datasets import Dataset, DatasetDict

def load_video_dataset(json_path):
    # 读取json文件
    with open(json_path, 'r') as file:
        data = json.load(file)

    # 准备转换后的数据列表
    transformed_data = {
        'video': [],
        'problem': [],
        'solution': [],
        'difficulty': []
    }

    # 遍历json数据并转换
    for entry in data:
        video_path = entry['video']
        difficulty = entry['difficulty']  # 获取difficulty字段
        problem = None  # 初始化问题变量
        for conversation in entry['conversations']:
            if conversation['from'] == 'human':
              #  problem = conversation['value'].replace('<video>\n<audio>\n', '')
                problem = "As an emotional recognition expert; throughout the video, which emotion conveyed by the characters is the most obvious to you?"

            elif conversation['from'] == 'gpt' and problem is not None:
                solution = f"<answer> {conversation['value']} </answer>"
                # 添加到transformed_data
                transformed_data['video'].append(video_path)
                transformed_data['problem'].append(problem)
                transformed_data['solution'].append(solution)
                transformed_data['difficulty'].append(difficulty)

    # 创建dataset
    dataset = Dataset.from_dict(transformed_data)
    dataset_dict = DatasetDict({'train': dataset})

    return dataset_dict


def main(script_args, training_args, model_args):
    # Get reward functions
    reward_funcs = [reward_funcs_registry[func] for func in script_args.reward_funcs]

    # Load the dataset
    json_file_path = script_args.dataset_name
    dataset = load_video_dataset(json_file_path)
   # dataset = load_dataset(script_args.dataset_name, name=script_args.dataset_config)
    
    # Format into conversation
    def make_conversation(example):
        return {
            "prompt": [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": example["problem"]},
            ],
        }

    # def make_conversation_image(example):
    #     return {
    #         "prompt": [
    #             {"role": "system", "content": [{"type": "text", "text": SYSTEM_PROMPT}]},
    #             {
    #                 "role": "user",
    #                 "content": [
    #                     {"type": "image"},
    #                     {"type": "text", "text": example["problem"]},
    #                 ],
    #             },
    #         ],
    #     }

    # QUESTION_TEMPLATE = "{Question}  Output the thinking process in <think> </think> and final answer (number) in <answer> </answer> tags."

    QUESTION_TEMPLATE = "{Question}\nOutput the thinking process in <think> </think> and final emotion in <answer> </answer> tags."
    def make_conversation_image(example):
        return {
            "prompt": [
                {
                    "role": "user",
                    "content": [
                        {"type": "image"},
                        {"type": "text", "text": QUESTION_TEMPLATE.format(Question=example["problem"])},
                    ],
                },
            ],
        }
    
    def make_conversation_video(example):
        return {
            "prompt": [
                {
                    "role": "user",
                    "content": [
                        {"type": "video"},
                        {"type": "text", "text": QUESTION_TEMPLATE.format(Question=example["problem"])},
                    ],
                },
            ],
        }
    
    if "image" in dataset[script_args.dataset_train_split].features:
        print("has image in dataset")
        dataset = dataset.map(make_conversation_image)  # Utilize multiprocessing for faster mapping
        # dataset = dataset.remove_columns(["original_question", "original_answer"])
    elif "video" in dataset[script_args.dataset_train_split].features:
        print("has video in dataset")
        dataset = dataset.map(make_conversation_video)
    else:
        print("no image in dataset")
        dataset = dataset.map(make_conversation)
        dataset = dataset.remove_columns("messages")

    
   # trainer_cls = Qwen2VLGRPOTrainer if not training_args.use_vllm else Qwen2VLGRPOVLLMTrainer
    trainer_cls = HumanOmniVLGRPOTrainer
    print("using: ", trainer_cls)

    # Initialize the GRPO trainer
    trainer = trainer_cls(
        model=model_args.model_name_or_path,
        reward_funcs=reward_funcs,
        args=training_args,
        train_dataset=dataset[script_args.dataset_train_split],
        eval_dataset=dataset[script_args.dataset_test_split] if training_args.eval_strategy != "no" else None,
        peft_config=get_peft_config(model_args),
        attn_implementation=model_args.attn_implementation,
        max_pixels=script_args.max_pixels,
        min_pixels=script_args.min_pixels,
    )

    # Train and push the model to the Hub
    trainer.train()

    # Save and push to hub
    trainer.save_model(training_args.output_dir)
    if training_args.push_to_hub:
        trainer.push_to_hub(dataset_name=script_args.dataset_name)


if __name__ == "__main__":
    parser = TrlParser((GRPOScriptArguments, GRPOConfig, ModelConfig))
    script_args, training_args, model_args = parser.parse_args_and_config()
    main(script_args, training_args, model_args)
